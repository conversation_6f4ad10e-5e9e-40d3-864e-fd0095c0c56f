import * as XLSX from "xlsx";

/**
 * Excel导出工具类
 */
class ExcelExporter {
  /**
   * 导出接线图明细列表到Excel
   * @param {Array} allNodeLinkList - 包含node和link数据的数组
   * @param {Object} mapInfo - 地图信息对象
   */
  static exportTopoDetailList(allNodeLinkList, mapInfo) {
    if (!allNodeLinkList || !Array.isArray(allNodeLinkList)) {
      throw new Error("allNodeLinkList必须是一个数组");
    }

    if (!mapInfo || !mapInfo.mapName) {
      throw new Error("mapInfo必须包含name属性");
    }

    // 分离node和link数据
    const nodes = [];
    const links = [];

    allNodeLinkList.forEach((item) => {
      if (item.nodeId) {
        nodes.push(item);
      } else if (item.linkId) {
        links.push(item);
      }
    });

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 处理电厂/站数据（nodes）
    if (nodes.length > 0) {
      const nodeSheetData = this.processNodeData(nodes);
      const nodeWorksheet = XLSX.utils.aoa_to_sheet(nodeSheetData);
      const nodeSheetName = this.sanitizeSheetName("电厂/站");
      XLSX.utils.book_append_sheet(workbook, nodeWorksheet, nodeSheetName);
    }

    // 处理线路数据（links）
    if (links.length > 0) {
      const linkSheetData = this.processLinkData(links);
      const linkWorksheet = XLSX.utils.aoa_to_sheet(linkSheetData);
      const linkSheetName = this.sanitizeSheetName("线路");
      XLSX.utils.book_append_sheet(workbook, linkWorksheet, linkSheetName);
    }

    // 生成文件名
    const fileName = `${mapInfo.name}接线图明细列表.xlsx`;

    // 导出文件
    XLSX.writeFile(workbook, fileName);
  }

  /**
   * 处理node数据，转换为Excel表格格式
   * @param {Array} nodes - node数据数组
   * @returns {Array} 二维数组格式的表格数据
   */
  static processNodeData(nodes) {
    if (nodes.length === 0) {
      return [["暂无数据"]];
    }

    // 获取所有可能的属性键
    const allKeys = this.getAllMetaDataKeys(nodes);

    // 创建表头，name放在第一列
    const headers = ["name", ...allKeys.filter((key) => key !== "name")];

    // 创建数据行
    const dataRows = nodes.map((node) => {
      const metaData = node.metaData || {};
      return headers.map((key) => {
        if (key === "name") {
          return metaData.name || "";
        }
        return metaData[key] || "";
      });
    });

    return [headers, ...dataRows];
  }

  /**
   * 处理link数据，转换为Excel表格格式
   * @param {Array} links - link数据数组
   * @returns {Array} 二维数组格式的表格数据
   */
  static processLinkData(links) {
    if (links.length === 0) {
      return [["暂无数据"]];
    }

    // 获取所有可能的属性键
    const allKeys = this.getAllMetaDataKeys(links);

    // 创建表头，name放在第一列
    const headers = ["name", ...allKeys.filter((key) => key !== "name")];

    // 创建数据行
    const dataRows = links.map((link) => {
      const metaData = link.metaData || {};
      return headers.map((key) => {
        if (key === "name") {
          return metaData.name || "";
        }
        return metaData[key] || "";
      });
    });

    return [headers, ...dataRows];
  }

  /**
   * 获取所有metaData中的属性键
   * @param {Array} dataList - 数据数组
   * @returns {Array} 所有属性键的数组
   */
  static getAllMetaDataKeys(dataList) {
    const keySet = new Set();

    dataList.forEach((item) => {
      const metaData = item.metaData || {};
      Object.keys(metaData).forEach((key) => {
        keySet.add(key);
      });
    });

    return Array.from(keySet).sort();
  }

  /**
   * 清理工作表名称，移除Excel不支持的特殊字符
   * @param {String} sheetName - 原始工作表名称
   * @returns {String} 清理后的工作表名称
   */
  static sanitizeSheetName(sheetName) {
    // Excel工作表名称不能包含: : \ / ? * [ ]
    return sheetName.replace(/[:\\/?*[\]]/g, "");
  }

  /**
   * 导出单个sheet的Excel文件
   * @param {Array} data - 二维数组格式的数据
   * @param {String} fileName - 文件名
   * @param {String} sheetName - sheet名称
   */
  static exportSingleSheet(data, fileName, sheetName = "Sheet1") {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    XLSX.writeFile(workbook, fileName);
  }

  /**
   * 将JSON数据导出为Excel
   * @param {Array} jsonData - JSON格式的数据数组
   * @param {String} fileName - 文件名
   * @param {String} sheetName - sheet名称
   */
  static exportJsonToExcel(jsonData, fileName, sheetName = "Sheet1") {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(jsonData);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    XLSX.writeFile(workbook, fileName);
  }
}

export default ExcelExporter;
