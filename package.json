{"name": "topo-tool", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix --ext .js,.vue"}, "dependencies": {"@svgdotjs/svg.js": "^3.1.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.27.2", "codemirror": "^5.41.0", "core-js": "^3.8.3", "d3": "^7.6.1", "dayjs": "^1.11.10", "file-saver": "^2.0.5", "jquery": "^3.6.0", "js-cookie": "^3.0.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "view-design": "^4.7.0", "vue": "^2.6.14", "vue-bus": "^1.2.1", "vue-print-nb": "^1.7.5", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "less": "^2.7.3", "less-loader": "^4.0.6", "lint-staged": "^11.1.2", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}, "gitHooks": {"pre-commit": "lint-staged"}}