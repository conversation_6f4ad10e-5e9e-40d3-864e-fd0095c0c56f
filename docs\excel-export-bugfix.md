# Excel导出功能Bug修复记录

## 问题描述

在使用Excel导出功能时遇到以下错误：
```
Excel导出失败: Error: Sheet name cannot contain : \ / ? * [ ]
```

## 问题原因

Excel工作表名称不能包含以下特殊字符：`: \ / ? * [ ]`

原代码中使用了"电厂/站"作为工作表名称，其中的斜杠字符`/`导致了错误。

## 解决方案

### 1. 立即修复
将工作表名称从"电厂/站"改为"电厂站"，移除了斜杠字符。

### 2. 长期解决方案
添加了`sanitizeSheetName()`方法来自动清理工作表名称中的非法字符：

```javascript
/**
 * 清理工作表名称，移除Excel不支持的特殊字符
 * @param {String} sheetName - 原始工作表名称
 * @returns {String} 清理后的工作表名称
 */
static sanitizeSheetName(sheetName) {
  // Excel工作表名称不能包含: : \ / ? * [ ]
  return sheetName.replace(/[:\\/?*[\]]/g, "");
}
```

### 3. 代码更新
更新了主导出方法，使用`sanitizeSheetName()`来处理工作表名称：

```javascript
// 处理电厂/站数据（nodes）
if (nodes.length > 0) {
  const nodeSheetData = this.processNodeData(nodes);
  const nodeWorksheet = XLSX.utils.aoa_to_sheet(nodeSheetData);
  const nodeSheetName = this.sanitizeSheetName("电厂/站");
  XLSX.utils.book_append_sheet(workbook, nodeWorksheet, nodeSheetName);
}

// 处理线路数据（links）
if (links.length > 0) {
  const linkSheetData = this.processLinkData(links);
  const linkWorksheet = XLSX.utils.aoa_to_sheet(linkSheetData);
  const linkSheetName = this.sanitizeSheetName("线路");
  XLSX.utils.book_append_sheet(workbook, linkWorksheet, linkSheetName);
}
```

## 修复结果

- ✅ 工作表名称"电厂/站"被清理为"电厂站"
- ✅ 工作表名称"线路"保持不变（无非法字符）
- ✅ 添加了防护机制，避免将来出现类似问题
- ✅ Excel文件可以正常生成和导出

## Excel工作表名称限制

根据Excel规范，工作表名称有以下限制：
1. 不能包含字符：`: \ / ? * [ ]`
2. 不能为空
3. 不能超过31个字符
4. 不能以单引号开头或结尾

## 测试验证

修复后的代码已通过以下测试：
- ✅ 正常数据导出测试
- ✅ 包含特殊字符的工作表名称测试
- ✅ 空数据处理测试
- ✅ 错误处理测试

## 预防措施

为了避免类似问题，建议：
1. 在设计工作表名称时避免使用特殊字符
2. 使用`sanitizeSheetName()`方法处理所有动态生成的工作表名称
3. 在测试时包含边界情况和特殊字符测试

## 相关文件

- `src/utils/excelExport.js` - 主要修复文件
- `src/components/Topo/Header.vue` - 调用导出功能的组件

## 版本信息

- 修复日期：2024年
- 修复版本：v1.0.1
- 影响范围：Excel导出功能
