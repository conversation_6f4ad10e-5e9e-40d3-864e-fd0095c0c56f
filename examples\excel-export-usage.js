/**
 * Excel导出工具使用示例
 * 展示如何在不同场景下使用ExcelExporter工具类
 */

import ExcelExporter from '@/utils/excelExport';

// 示例1: 基本的接线图数据导出
function exportTopoData() {
  // 模拟从Vue组件中获取的数据
  const allNodeLinkList = [
    {
      nodeId: "node1",
      metaData: {
        name: "变电站A",
        type: "Substation",
        voltage: "_220KV",
        location: "江苏南京"
      }
    },
    {
      linkId: "link1", 
      metaData: {
        name: "线路A-B",
        type: "TransmissionLine",
        voltage: "_500KV",
        length: "100km"
      }
    }
  ];

  const mapInfo = {
    mapId: "map123",
    name: "华东电网"
  };

  try {
    ExcelExporter.exportTopoDetailList(allNodeLinkList, mapInfo);
    console.log('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
  }
}

// 示例2: 在Vue组件中使用
const VueComponentExample = {
  data() {
    return {
      allNodeLinkList: [],
      mapInfo: {}
    };
  },
  methods: {
    async handleExportClick() {
      // 检查数据
      if (!this.mapInfo.mapId) {
        this.$Message.warning("请先选择图层");
        return;
      }

      if (!this.allNodeLinkList.length) {
        this.$Message.warning("暂无数据可导出");
        return;
      }

      // 显示加载状态
      this.$Loading.start();

      try {
        // 执行导出
        ExcelExporter.exportTopoDetailList(this.allNodeLinkList, this.mapInfo);
        this.$Message.success("Excel导出成功");
      } catch (error) {
        console.error("Excel导出失败:", error);
        this.$Message.error(`Excel导出失败: ${error.message}`);
      } finally {
        this.$Loading.finish();
      }
    }
  }
};

// 示例3: 导出自定义数据格式
function exportCustomData() {
  // 自定义的JSON数据
  const customData = [
    {
      设备名称: "主变1",
      设备类型: "变压器", 
      额定容量: "500MVA",
      运行状态: "正常"
    },
    {
      设备名称: "主变2",
      设备类型: "变压器",
      额定容量: "500MVA", 
      运行状态: "检修"
    }
  ];

  ExcelExporter.exportJsonToExcel(
    customData,
    "设备清单.xlsx",
    "设备信息"
  );
}

// 示例4: 导出单个工作表
function exportSingleSheetData() {
  // 二维数组格式的数据
  const tableData = [
    ["设备编号", "设备名称", "电压等级", "投运日期"],
    ["001", "变电站A", "500kV", "2020-01-01"],
    ["002", "变电站B", "220kV", "2021-06-15"],
    ["003", "开关站C", "110kV", "2022-03-20"]
  ];

  ExcelExporter.exportSingleSheet(
    tableData,
    "设备统计表.xlsx",
    "设备统计"
  );
}

// 示例5: 批量处理多个图层数据
async function exportMultipleMapData(mapDataList) {
  for (const mapData of mapDataList) {
    try {
      const { allNodeLinkList, mapInfo } = mapData;
      
      // 为每个图层生成独立的Excel文件
      ExcelExporter.exportTopoDetailList(allNodeLinkList, mapInfo);
      
      // 添加延迟避免浏览器下载限制
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`导出图层 ${mapData.mapInfo.name} 失败:`, error);
    }
  }
}

// 示例6: 带数据预处理的导出
function exportWithDataPreprocessing(rawData, mapInfo) {
  // 数据预处理：过滤和转换
  const processedData = rawData
    .filter(item => item.metaData && item.metaData.name) // 过滤无效数据
    .map(item => {
      // 数据转换
      const processed = { ...item };
      if (processed.metaData) {
        // 格式化日期
        if (processed.metaData.createTime) {
          processed.metaData.createTime = new Date(processed.metaData.createTime)
            .toLocaleDateString('zh-CN');
        }
        
        // 转换状态码为中文
        if (processed.metaData.status === 'RUNNING') {
          processed.metaData.status = '运行';
        } else if (processed.metaData.status === 'STOPPED') {
          processed.metaData.status = '停运';
        }
      }
      return processed;
    });

  // 执行导出
  ExcelExporter.exportTopoDetailList(processedData, mapInfo);
}

// 示例7: 错误处理最佳实践
function exportWithErrorHandling(allNodeLinkList, mapInfo, onSuccess, onError) {
  try {
    // 参数验证
    if (!Array.isArray(allNodeLinkList)) {
      throw new Error('allNodeLinkList必须是数组');
    }
    
    if (!mapInfo || typeof mapInfo.name !== 'string') {
      throw new Error('mapInfo必须包含有效的name属性');
    }

    // 数据验证
    const validData = allNodeLinkList.filter(item => 
      item && (item.nodeId || item.linkId) && item.metaData
    );

    if (validData.length === 0) {
      throw new Error('没有有效的数据可导出');
    }

    // 执行导出
    ExcelExporter.exportTopoDetailList(validData, mapInfo);
    
    // 成功回调
    if (typeof onSuccess === 'function') {
      onSuccess({
        totalCount: allNodeLinkList.length,
        validCount: validData.length,
        fileName: `${mapInfo.name}接线图明细列表.xlsx`
      });
    }

  } catch (error) {
    // 错误回调
    if (typeof onError === 'function') {
      onError(error);
    } else {
      console.error('Excel导出失败:', error);
    }
  }
}

// 导出示例函数供外部使用
export {
  exportTopoData,
  VueComponentExample,
  exportCustomData,
  exportSingleSheetData,
  exportMultipleMapData,
  exportWithDataPreprocessing,
  exportWithErrorHandling
};
