# Excel导出功能使用说明

## 功能概述

本功能实现了将接线图中的电厂/站和线路数据导出为Excel文件的功能。导出的Excel文件包含两个工作表：
- **电厂/站**：包含所有node类型的数据
- **线路**：包含所有link类型的数据

## 使用方法

1. 在拓扑编辑器界面中，确保已选择图层
2. 点击顶部工具栏中的"导出Excel"按钮
3. 系统会自动生成Excel文件并下载，文件名格式为：`{地图名称}接线图明细列表.xlsx`

## 数据结构说明

### 输入数据
- `allNodeLinkList`：包含node和link数据的数组
- `mapInfo`：地图信息对象，必须包含`name`属性

### 数据分类
系统会自动识别数据类型：
- 包含`nodeId`的数据归类为电厂/站
- 包含`linkId`的数据归类为线路

### metaData处理
每个数据项的`metaData`属性会被解析为Excel表格的列：
- `name`字段固定作为第一列
- 其他属性按字母顺序排列为后续列
- 空值会显示为空单元格

## 示例数据格式

```javascript
// node数据示例
{
  nodeId: "123",
  metaData: {
    name: "江苏.天目湖",
    type: "TransStation",
    volt: "_500KV",
    highBusDesc5: "江苏.天目湖/500kV.500kVⅠ段母线,_500KV",
    highBusDesc6: "江苏.天目湖/500kV.500kVⅡ段母线,_500KV",
    // ... 其他属性
  }
}

// link数据示例
{
  linkId: "456",
  metaData: {
    name: "某线路",
    type: "TransmissionLine",
    voltage: "_220KV",
    // ... 其他属性
  }
}
```

## 错误处理

系统会处理以下错误情况：
1. **未选择图层**：提示"请先选择图层"
2. **无数据可导出**：提示"暂无数据可导出"
3. **导出失败**：显示具体错误信息

## 技术实现

### 核心文件
- `src/utils/excelExport.js`：Excel导出工具类
- `src/components/Topo/Header.vue`：界面组件，包含导出按钮和处理方法

### 依赖库
- `xlsx`：用于Excel文件的生成和导出

### 主要方法
- `ExcelExporter.exportTopoDetailList()`：主导出方法
- `processNodeData()`：处理电厂/站数据
- `processLinkData()`：处理线路数据
- `getAllMetaDataKeys()`：获取所有属性键

## 扩展功能

工具类还提供了其他导出方法：
- `exportSingleSheet()`：导出单个工作表
- `exportJsonToExcel()`：将JSON数据导出为Excel

## 注意事项

1. 确保已安装`xlsx`依赖：`npm install xlsx`
2. 导出的Excel文件会自动下载到浏览器默认下载目录
3. 如果数据量很大，导出过程可能需要一些时间
4. 建议在导出前检查数据的完整性

## 故障排除

### 常见问题
1. **导出按钮无响应**
   - 检查是否已选择图层
   - 检查控制台是否有错误信息

2. **文件下载失败**
   - 检查浏览器下载设置
   - 确认浏览器允许文件下载

3. **Excel文件打开异常**
   - 确认使用的Excel版本支持.xlsx格式
   - 检查文件是否完整下载

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的请求状态

## 更新日志

### v1.0.0
- 实现基本的Excel导出功能
- 支持电厂/站和线路数据分别导出到不同工作表
- 自动处理metaData属性映射
- 添加错误处理和用户提示
